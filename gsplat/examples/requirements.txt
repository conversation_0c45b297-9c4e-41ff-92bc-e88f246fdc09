# assume torch is already installed

# pycolmap for data parsing
git+https://github.com/rmbrualla/pycolmap@cc7ea4b7301720ac29287dbe450952511b32125e
# (optional) nerfacc for torch version rasterization 
# git+https://github.com/nerfstudio-project/nerfacc

viser
git+https://github.com/nerfstudio-project/nerfview@4538024fe0d15fd1a0e4d760f3695fc44ca72787
imageio[ffmpeg]
numpy<2.0.0
scikit-learn
tqdm
torchmetrics[image]
opencv-python
tyro>=0.8.8
Pillow
tensorboard
tensorly
pyyaml
matplotlib
git+https://github.com/rahul-goel/fused-ssim@328dc9836f513d00c4b5bc38fe30478b4435cbb5
git+https://github.com/harry7557558/fused-bilagrid@90f9788e57d3545e3a033c1038bb9986549632fe
splines
