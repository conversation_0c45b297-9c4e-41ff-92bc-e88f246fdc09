# To set up the clangd config.
# 1. Activate an environment with cuda installed. (probably via conda, skip if using system CUDA)
# 2. Run:
#       echo "# Autogenerated, see .clangd_template\!" > .clangd && sed -e "/^#/d" -e "s|YOUR_CUDA_PATH|$(dirname $(dirname $(which nvcc)))|" .clangd_template >> .clangd
CompileFlags:
  Add:
    - -Xclang
    - -fcuda-allow-variadic-functions 
    - --cuda-path=YOUR_CUDA_PATH
  Remove: 
    - --diag_suppress=*
    - --generate-code=*
    - -gencode=*
    - -forward-unknown-to-host-compiler
    - -Xcompiler
    - -Xcudafe
    - --use_fast_math
    - --options-file
    - --compiler-options
    - --expt-relaxed-constexpr
