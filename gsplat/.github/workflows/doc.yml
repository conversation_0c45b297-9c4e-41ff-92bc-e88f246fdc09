name: Docs
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  release:
    types: [created]
    branches: [main]
  workflow_dispatch:

permissions:
  contents: write
jobs:
  docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'recursive'
          
      - uses: actions/setup-python@v3
        with:
          python-version: '3.9'
      
      - name: Install dependencies
        run: |
          pip install -r docs/requirements.txt
          pip install torch==2.0.0 --index-url https://download.pytorch.org/whl/cpu
          BUILD_NO_CUDA=1 pip install .
      
      # Get version.
      - name: Get version + subdirectory
        run: |
          VERSION=$(python -c "from gsplat import __version__; print(__version__)")
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "DOCS_SUBDIR=versions/$VERSION" >> $GITHUB_ENV

      # Hack to overwrite version.
      - name: Set version to 'main' for pushes (this will appear in the doc banner)
        run: |
          sed -i 's/__version__ = ".*"/__version__ = "main"/' gsplat/version.py
        if: github.event_name == 'push'

      # Get version.
      - name: Override subdirectory to `main/` for pushes
        run: |
          echo "DOCS_SUBDIR=main" >> $GITHUB_ENV
        if: github.event_name == 'push'

      - name: Sphinx build
        # fail on warnings: "-W --keep-going"
        run: |
          sphinx-build docs/source _build

      # Deploy to version-dependent subdirectory.
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: _build/
          destination_dir: ${{ env.DOCS_SUBDIR }}
          keep_files: false  # This will only erase the destination subdirectory.
          cname: docs.gsplat.studio
        if: github.event_name != 'pull_request'
  
      # We'll maintain an index of all versions under docs.gsplat.studio/versions.
      # This will be useful for dynamically generating lists of possible doc links.
      - name: Update versions index.txt
        run: |
          git fetch
          git checkout .  # Revert change to version.py from earlier...
          git checkout gh-pages
          git pull
          git config --global user.email "<EMAIL>"
          git config --global user.name "Ruilong Li"
          FILE="versions/index.txt"  # Replace with your file path
          if ! grep -qx "$VERSION" "$FILE"; then
            echo "$VERSION" >> "$FILE"
            git add $FILE
            git commit -m "Update versions.txt with new version $VERSION"
            git push origin gh-pages
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERSION: ${{ env.VERSION }}
        if: github.event_name == 'release'
