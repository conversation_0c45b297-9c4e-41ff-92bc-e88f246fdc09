name: Build Wheels

on: [workflow_call, workflow_dispatch]
jobs:
  build_sdist:
    name: Build source distribution and no binary wheel
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Strip unsupported tags in README
        run: |
          sed -i '/<!-- pypi-strip -->/,/<!-- \/pypi-strip -->/d' README.md
      - name: Build sdist
        run: BUILD_NO_CUDA=1 pipx run build --sdist
      - name: Build wheel with no binaries
        run: BUILD_NO_CUDA=1 python setup.py bdist_wheel --dist-dir=dist
      - uses: actions/upload-artifact@v4
        with:
          name: pypi_packages
          path: dist/*.tar.gz
 
  build_wheels:
    runs-on: ${{ matrix.os }}
    environment: production

    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04, windows-2019]
        python-version: ['3.10']
        torch-version: ['2.0.0', '2.1.0', '2.2.0', '2.3.0', '2.4.0']
        cuda-version: ['cu118', 'cu121', 'cu124']
        exclude:
          - python-version: 3.12
            torch-version: 2.0.0
          - python-version: 3.12
            torch-version: 2.1.0
          - torch-version: 2.0.0
            cuda-version: 'cu113'
          - torch-version: 2.0.0
            cuda-version: 'cu116'
          - torch-version: 2.0.0
            cuda-version: 'cu121'
          - torch-version: 2.0.0
            cuda-version: 'cu124'
          - torch-version: 2.1.0
            cuda-version: 'cu113'
          - torch-version: 2.1.0
            cuda-version: 'cu116'
          - torch-version: 2.1.0
            cuda-version: 'cu117'
          - torch-version: 2.1.0
            cuda-version: 'cu124'
          - torch-version: 2.2.0
            cuda-version: 'cu113'
          - torch-version: 2.2.0
            cuda-version: 'cu116'
          - torch-version: 2.2.0
            cuda-version: 'cu117'
          - torch-version: 2.2.0
            cuda-version: 'cu124'
          - torch-version: 2.3.0
            cuda-version: 'cu113'
          - torch-version: 2.3.0
            cuda-version: 'cu116'
          - torch-version: 2.3.0
            cuda-version: 'cu117'
          - torch-version: 2.3.0
            cuda-version: 'cu124'
          - torch-version: 2.4.0
            cuda-version: 'cu113'
          - torch-version: 2.4.0
            cuda-version: 'cu116'
          - torch-version: 2.4.0
            cuda-version: 'cu117'
          - os: windows-2019
            torch-version: 2.0.0
            cuda-version: 'cu121'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive
  
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Free up disk space
        if: ${{ runner.os == 'Linux' }}
        run: |
          echo "Disk space before cleanup:"
          df -h
          sudo rm -rf /usr/share/dotnet
          echo "Disk space after cleanup:"
          df -h
        shell: bash

      - name: Install CUDA ${{ matrix.cuda-version }}
        if: ${{ matrix.cuda-version != 'cpu' }}
        run: |
          bash .github/workflows/cuda/${{ runner.os }}.sh ${{ matrix.cuda-version }}
        shell: bash

      - name: Install PyTorch ${{ matrix.torch-version }}+${{ matrix.cuda-version }}
        run: |
          pip install torch==${{ matrix.torch-version }} --extra-index-url https://download.pytorch.org/whl/${{ matrix.cuda-version }}
          python -c "import torch; print('PyTorch:', torch.__version__)"
          python -c "import torch; print('CUDA:', torch.version.cuda)"
          python -c "import torch; print('CUDA Available:', torch.cuda.is_available())"
        shell: bash

      - name: Patch PyTorch static constexpr on Windows
        if: ${{ runner.os == 'Windows' }}
        run: |
          Torch_DIR=`python -c 'import os; import torch; print(os.path.dirname(torch.__file__))'`
          sed -i '31,38c\
          TORCH_API void lazy_init_num_threads();' ${Torch_DIR}/include/ATen/Parallel.h
        shell: bash

      - name: Set version
        if: ${{ runner.os != 'macOS' }}
        run: |
          VERSION=`sed -n 's/^__version__ = "\(.*\)"/\1/p' gsplat/version.py`
          TORCH_VERSION=`echo "pt${{ matrix.torch-version }}" | sed "s/..$//" | sed "s/\.//g"`
          CUDA_VERSION=`echo ${{ matrix.cuda-version }}`
          echo "New version name: $VERSION+$TORCH_VERSION$CUDA_VERSION"
          sed -i "s/$VERSION/$VERSION+$TORCH_VERSION$CUDA_VERSION/" gsplat/version.py
        shell: bash

      - name: Upgrade pip
        run: |
          pip install --upgrade setuptools
          pip install ninja
        shell: bash

      - name: Install main package for CPU
        if: ${{ matrix.cuda-version == 'cpu' }}
        run: |
          BUILD_NO_CUDA=1 pip install .
        shell: bash

      - name: Build wheel
        run: |
          pip install wheel
          source .github/workflows/cuda/${{ runner.os }}-env.sh ${{ matrix.cuda-version }}
          MAX_JOBS=2 python setup.py bdist_wheel --dist-dir=dist
        shell: bash

      - name: Test wheel
        run: |
          cd dist
          ls -lah
          pip install *.whl
          python -c "import gsplat; print('gsplat:', gsplat.__version__)"
          cd ..
        shell: bash

      - uses: actions/upload-artifact@v4
        with:
          # Include unique matrix values to avoid name collisions.
          name: compiled_wheels_python${{ matrix.python-version }}-${{ matrix.os }}-${{ matrix.torch-version }}-${{ matrix.cuda-version }}
          path: dist/*.whl
