# This workflows will upload a Python Package using twine when a release is created
# For more information see: https://help.github.com/en/actions/language-and-framework-guides/using-python-with-github-actions#publishing-to-package-registries

name: Update wheels index pages

# trigger manually on github interface
on:
  workflow_dispatch:
  push:
    branches: [generate_wheels_simple_index_pages]

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'recursive'
          
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.x'
     
      - name: Install dependencies
        run: |
          python -m pip install requests jinja2
      # call the script to generate the simple index pages
      - name: Generate Simple Index Pages
        run: python .github/workflows/generate_simple_index_pages.py --outdir ./whl
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  

      
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./whl  # Directory where the simple index pages are located
          destination_dir: whl  # The 'wh' folder in the GitHub Pages root