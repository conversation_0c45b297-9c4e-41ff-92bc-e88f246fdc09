# Build and Release Wheels

name: Build and Release Wheels

on:
  release:
    types: [created]
  workflow_dispatch:

permissions:
  contents: write

jobs:
  # Build the wheels using the reusable building workflow
  build_wheels:
    name: Call reusable building workflow
    uses: ./.github/workflows/building.yml  

  create_release_and_upload_packages:
    name: Upload to GitHub Release
    needs: [build_wheels]
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ['3.10']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download packages
        id: download_artifacts
        uses: actions/download-artifact@v4
        with:
          # The unique artifact names from building.yml all start with
          # "compiled_wheels_python${{ matrix.python-version }}" so this pattern
          # will match them all and merge them into the 'dist' directory.
          pattern: compiled_wheels_python${{ matrix.python-version }}*
          path: dist
          merge-multiple: true

      - name: Upload packages to latest GitHub Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo "Fetching latest release info (unauthenticated)..."
          release_info=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
            https://api.github.com/repos/nerfstudio-project/gsplat/releases/latest)

          # Extract the "upload_url" field and strip the {?name,label} part
          upload_url=$(echo "$release_info" | grep '"upload_url":' | cut -d '"' -f 4 | sed 's/{.*//')
          echo "Upload URL: $upload_url"

          for file in ./dist/*.*; do
            echo "Uploading $file..."
            filename=$(basename "$file")
            encoded_filename=$(echo "$filename" | sed 's/+/%2B/g')
            curl -X POST \
              -H "Authorization: token $GITHUB_TOKEN" \
              -H "Content-Type: application/zip" \
              --data-binary @"$file" \
              "$upload_url?name=$encoded_filename"
          done
          echo "Upload complete." 

  generate_simple_index_pages:
    name: Generate Simple Index Pages
    needs: [create_release_and_upload_packages]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Generate Simple Index Pages
        run: python .github/workflows/generate_simple_index_pages.py --outdir ./whl
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./whl
          destination_dir: whl
          keep_files: false
          cname: docs.gsplat.studio

  upload_pypi:
    name: Upload to PyPi
    needs: [build_wheels]
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install dependencies
        run: |
          python -m pip install build twine
        shell: bash

      - name: Publish package to PyPI
        env:
          PYPI_TOKEN: ${{ secrets.PYPI_TOKEN }}
        run: |
          BUILD_NO_CUDA=1 python -m build
          twine upload --username __token__ --password $PYPI_TOKEN dist/*
        shell: bash
