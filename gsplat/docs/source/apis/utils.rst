
Utils
===================================

Below are the basic functions that supports the rasterization.

3DGS
-----

.. currentmodule:: gsplat

.. autofunction:: spherical_harmonics

.. autofunction:: quat_scale_to_covar_preci

.. autofunction:: proj

.. autofunction:: fully_fused_projection

.. autofunction:: isect_tiles

.. autofunction:: isect_offset_encode

.. autofunction:: world_to_cam

.. autofunction:: rasterize_to_pixels

.. autofunction:: rasterize_to_indices_in_range

.. autofunction:: accumulate

.. autofunction:: rasterization_inria_wrapper

2DGS
-----
.. currentmodule:: gsplat

.. autofunction:: fully_fused_projection_2dgs

.. autofunction:: rasterize_to_pixels_2dgs

.. autofunction:: rasterize_to_indices_in_range_2dgs

.. autofunction:: accumulate_2dgs

.. autofunction:: rasterization_2dgs_inria_wrapper