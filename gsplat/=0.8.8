Looking in indexes: https://pypi.org/simple, https://pypi.ngc.nvidia.com
Collecting tyro
  Downloading tyro-0.9.17-py3-none-any.whl (123 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 123.7/123.7 kB 17.7 MB/s eta 0:00:00
Requirement already satisfied: typing-extensions>=4.7.0 in /ghome/l1/yliu/mambaforge/envs/gaussian_splatting/lib/python3.7/site-packages (from tyro) (4.7.1)
Collecting shtab>=1.5.6
  Downloading shtab-1.7.2-py3-none-any.whl (14 kB)
Collecting docstring-parser>=0.15
  Downloading docstring_parser-0.16-py3-none-any.whl (36 kB)
Requirement already satisfied: rich>=11.1.0 in /ghome/l1/yliu/mambaforge/envs/gaussian_splatting/lib/python3.7/site-packages (from tyro) (13.8.1)
Collecting eval-type-backport>=0.1.3
  Downloading eval_type_backport-0.1.3-py3-none-any.whl (5.6 kB)
Collecting backports-cached-property>=1.0.2
  Downloading backports.cached_property-1.0.2-py3-none-any.whl (6.1 kB)
Requirement already satisfied: typeguard>=4.0.0 in /ghome/l1/yliu/mambaforge/envs/gaussian_splatting/lib/python3.7/site-packages (from tyro) (4.1.2)
Requirement already satisfied: markdown-it-py>=2.2.0 in /ghome/l1/yliu/mambaforge/envs/gaussian_splatting/lib/python3.7/site-packages (from rich>=11.1.0->tyro) (2.2.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /ghome/l1/yliu/mambaforge/envs/gaussian_splatting/lib/python3.7/site-packages (from rich>=11.1.0->tyro) (2.17.2)
Requirement already satisfied: importlib-metadata>=3.6 in /ghome/l1/yliu/mambaforge/envs/gaussian_splatting/lib/python3.7/site-packages (from typeguard>=4.0.0->tyro) (6.7.0)
Requirement already satisfied: zipp>=0.5 in /ghome/l1/yliu/mambaforge/envs/gaussian_splatting/lib/python3.7/site-packages (from importlib-metadata>=3.6->typeguard>=4.0.0->tyro) (3.15.0)
Requirement already satisfied: mdurl~=0.1 in /ghome/l1/yliu/mambaforge/envs/gaussian_splatting/lib/python3.7/site-packages (from markdown-it-py>=2.2.0->rich>=11.1.0->tyro) (0.1.2)
Installing collected packages: shtab, eval-type-backport, docstring-parser, backports-cached-property, tyro
Successfully installed backports-cached-property-1.0.2 docstring-parser-0.16 eval-type-backport-0.1.3 shtab-1.7.2 tyro-0.9.17
