#!/usr/bin/env python3

"""
测试球面高斯模型的基本功能
"""

import torch
import numpy as np
from scene.spherical_gaussian_model import SphericalGaussianModel
from utils.graphics_utils import BasicPointCloud

def test_spherical_gaussian_model():
    """测试球面高斯模型的基本功能"""
    print("Testing Spherical Gaussian Model...")
    
    # 创建测试点云数据
    num_points = 100
    points = np.random.randn(num_points, 3).astype(np.float32)
    colors = np.random.rand(num_points, 3).astype(np.float32)
    normals = np.random.randn(num_points, 3).astype(np.float32)
    
    pcd = BasicPointCloud(points=points, colors=colors, normals=normals)
    
    # 创建球面高斯模型
    max_sg_degree = 2
    sg_model = SphericalGaussianModel(max_sg_degree=max_sg_degree)
    
    print(f"Created model with max_sg_degree={max_sg_degree}")
    print(f"Active SG degree: {sg_model.active_sg_degree}")
    
    # 从点云初始化
    sg_model.create_from_pcd(pcd, None, 1.0)
    
    print(f"Initialized from point cloud with {num_points} points")
    print(f"Model xyz shape: {sg_model.get_xyz.shape}")
    print(f"Model rgb_base shape: {sg_model.get_rgb_base.shape}")
    
    # 测试球面高斯基的初始化
    for degree in range(1, max_sg_degree + 1):
        num_bases = sg_model._get_num_bases_for_degree(degree)
        print(f"Degree {degree} has {num_bases} bases")
        
        for base_idx in range(num_bases):
            directions = sg_model.get_sg_directions(degree, base_idx)
            sharpness = sg_model.get_sg_sharpness(degree, base_idx)
            rgb = sg_model.get_sg_rgb(degree, base_idx)
            
            if directions is not None:
                print(f"  Base {base_idx}: directions shape {directions.shape}, sharpness shape {sharpness.shape}, rgb shape {rgb.shape}")
    
    # 测试增加SG等级
    print("\nTesting SG degree increase...")
    sg_model.oneupSGdegree()
    print(f"Active SG degree after oneupSGdegree: {sg_model.active_sg_degree}")
    
    # 创建模拟相机
    class MockCamera:
        def __init__(self):
            self.camera_center = torch.tensor([0.0, 0.0, 5.0], device="cuda")
    
    mock_camera = MockCamera()
    
    # 测试颜色计算
    print("\nTesting color computation...")
    try:
        colors_precomp = sg_model.compute_colors_precomp(mock_camera)
        print(f"Computed colors shape: {colors_precomp.shape}")
        print(f"Colors range: [{colors_precomp.min().item():.3f}, {colors_precomp.max().item():.3f}]")
        print("Color computation successful!")
    except Exception as e:
        print(f"Error in color computation: {e}")
    
    # 测试属性访问
    print("\nTesting property access...")
    print(f"Scaling shape: {sg_model.get_scaling.shape}")
    print(f"Rotation shape: {sg_model.get_rotation.shape}")
    print(f"Opacity shape: {sg_model.get_opacity.shape}")
    
    # 测试保存和加载功能
    print("\nTesting save/load functionality...")
    try:
        # 测试构建属性列表
        attr_list = sg_model.construct_list_of_attributes()
        print(f"Attribute list length: {len(attr_list)}")
        print(f"First few attributes: {attr_list[:10]}")
        
        # 测试捕获状态
        captured_state = sg_model.capture()
        print(f"Captured state keys: {list(captured_state.keys())}")
        print("Save/load functionality test passed!")
    except Exception as e:
        print(f"Error in save/load test: {e}")
    
    print("\nAll tests completed!")

def test_directional_scaling():
    """测试方向性缩放计算"""
    print("\nTesting directional scaling computation...")
    
    # 创建测试数据
    batch_size = 10
    directions = torch.randn(batch_size, 3, device="cuda")
    directions = torch.nn.functional.normalize(directions, dim=1)
    
    sharpness = torch.ones(batch_size, 1, device="cuda") * 2.0
    
    view_dirs = torch.randn(batch_size, 3, device="cuda")
    view_dirs = torch.nn.functional.normalize(view_dirs, dim=1)
    
    # 计算余弦相似度
    cos_theta = torch.sum(directions * view_dirs, dim=1, keepdim=True)
    
    # 计算方向性缩放
    directional_scale = torch.exp(sharpness * (cos_theta - 1.0))
    
    print(f"Directions shape: {directions.shape}")
    print(f"View directions shape: {view_dirs.shape}")
    print(f"Cos theta range: [{cos_theta.min().item():.3f}, {cos_theta.max().item():.3f}]")
    print(f"Directional scale range: [{directional_scale.min().item():.3f}, {directional_scale.max().item():.3f}]")
    
    # 测试完全对齐的情况
    aligned_dirs = torch.tensor([[1.0, 0.0, 0.0]], device="cuda")
    aligned_view = torch.tensor([[1.0, 0.0, 0.0]], device="cuda")
    aligned_sharpness = torch.tensor([[1.0]], device="cuda")
    
    aligned_cos = torch.sum(aligned_dirs * aligned_view, dim=1, keepdim=True)
    aligned_scale = torch.exp(aligned_sharpness * (aligned_cos - 1.0))
    
    print(f"Aligned case - cos_theta: {aligned_cos.item():.3f}, scale: {aligned_scale.item():.3f}")
    
    # 测试完全相反的情况
    opposite_view = torch.tensor([[-1.0, 0.0, 0.0]], device="cuda")
    opposite_cos = torch.sum(aligned_dirs * opposite_view, dim=1, keepdim=True)
    opposite_scale = torch.exp(aligned_sharpness * (opposite_cos - 1.0))
    
    print(f"Opposite case - cos_theta: {opposite_cos.item():.3f}, scale: {opposite_scale.item():.3f}")
    
    print("Directional scaling test completed!")

if __name__ == "__main__":
    # 确保CUDA可用
    if not torch.cuda.is_available():
        print("CUDA is not available. This test requires CUDA.")
        exit(1)
    
    print("CUDA is available. Starting tests...")
    
    try:
        test_spherical_gaussian_model()
        test_directional_scaling()
        print("\n✅ All tests passed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
